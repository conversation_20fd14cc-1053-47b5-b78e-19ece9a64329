from dotenv import load_dotenv

load_dotenv()

import os

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

from langchain import LLMChain, PromptTemplate
from langchain.llms import OpenAI

def summarize_text(text: str) -> str:

    """
    Uses an LLMChain to generate a concise summary of `text`.
    """

    llm = OpenAI(
        openai_api_key = OPENAI_API_KEY
        temperature = 0.3 # Lower temperature for more deterministic output
    )

    # Define how we ask the model to summarize

    prompt = PromptTemplate(
        input_variables = ["text"],
        template = (
            "You are an email assistant. "
            "Please provide a concise summary of the following email content:\n\n"
            "{text}\n\n"
            "Summary:"
        )
    )

    chain = LLMChain(llm=llm, prompt=prompt) # Chain the LLM with the prompt
    summary = chain.run(text = text) # Run the chain with the input text
    return summary.strip() # Clean up whitespace and return the summary
    